"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, CreditCard, ArrowLeft } from "lucide-react";
import { useCreatePayment } from "@/hooks/use-query";

/**
 * Payment Selection Form Component
 *
 * This component handles payment creation and processing for immigration service applications.
 * It integrates with the backend payment API (POST /v2/payment) to create payments for all
 * payment methods including Stripe, Cash, Bank Deposit, and Online Transfer.
 *
 * API Integration:
 * - Endpoint: POST /v2/payment
 * - Payload Structure:
 *   {
 *     "amount": number,           // Product amount after discount is applied
 *     "user_id": string,          // User identifier
 *     "serviceType": "immigration", // Service type (currently only immigration)
 *     "serviceId": string,        // Immigration service ID
 *     "discount_amount": number,  // Optional: discount amount deducted from actual price
 *     "actual_amount": number,    // Original product price before discount
 *     "payment_method": string,   // "stripe" | "cash" | "bank_deposit" | "online_transfer"
 *     "transactionId": string     // Optional for all payment methods
 *   }
 *
 * Payment Flow Changes (v2.0.0 - 2025-07-08):
 * - Stripe Payments: Auto-completes application creation and redirects to records page
 * - Non-Stripe Payments: Continues to workflow template selection after payment creation
 * - Transaction ID is now truly optional for all payment methods (improved UX)
 * - Discount amount validation fixed to always send integer values
 * - Workflow template selection maintained for user choice
 *
 * Response Handling:
 * - For Stripe payments: Auto-triggers application completion workflow
 * - For other payments: Returns payment_id for application creation and continues workflow
 * - All payments redirect to /applications page upon successful completion
 *
 * Error Handling:
 * - Comprehensive validation with user-friendly error messages
 * - Session management for authentication errors
 * - Form-level error display for better UX
 * - Specific error handling for missing Stripe links
 *
 * Features:
 * - Works for both authenticated and unauthenticated users
 * - Supports admin and agent user scenarios
 * - Memory leak prevention through proper cleanup
 * - Preserves existing UI design and functionality
 * - Enhanced Stripe link display with copy and external link functionality
 */

/**
 * Payment Selection Form Validation Schema
 *
 * Validates payment form data with the following rules:
 * - Payment type is required (Stripe, Cash, Bank Deposit, Online Transfer)
 * - Transaction ID is optional for all payment methods (truly optional now)
 * - Payment ID functionality has been removed as per requirements
 */
const paymentSelectionSchema = z.object({
  paymentType: z.enum(["Stripe", "Cash", "Bank Deposit", "Online Transfer"], {
    required_error: "Please select a payment type",
  }),
  transactionId: z.string().optional(), // Optional for all payment methods including non-Stripe
});

type FormData = z.infer<typeof paymentSelectionSchema>;

interface PaymentSelectionFormProps {
  onNext: (data: {
    paymentMethod: string;
    paymentType?: string;
    paymentId?: string;
    transactionId?: string;
    payments?: string[];
    stripeLink?: string;
    autoComplete?: boolean; // Flag to indicate auto-completion for Stripe payments
  }) => void;
  onBack: () => void;
  initialData?: {
    paymentMethod?: string;
    paymentType?: string;
    transactionId?: string;
  };
  originalPrice: number;
  discountedPrice: number;
  userId: string;
  immigrationServiceId: string;
  applicationData?: any; // For storing application data for auto-completion
}

export const PaymentSelectionForm: React.FC<PaymentSelectionFormProps> = ({
  onNext,
  onBack,
  initialData,
  originalPrice,
  discountedPrice,
  userId,
  immigrationServiceId,
  applicationData,
}) => {
  const [loading, setLoading] = useState(false);
  const createPaymentMutation = useCreatePayment();

  const form = useForm<FormData>({
    resolver: zodResolver(paymentSelectionSchema),
    defaultValues: {
      paymentType: (initialData?.paymentMethod as any) || undefined,
      transactionId: initialData?.transactionId || "",
    },
  });

  const paymentType = form.watch("paymentType");

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      // Map payment types to backend format
      const paymentMethodMap: Record<string, string> = {
        Stripe: "stripe",
        Cash: "cash",
        "Bank Deposit": "bank_deposit",
        "Online Transfer": "online_transfer",
      };

      const paymentMethod = paymentMethodMap[data.paymentType];
      // Ensure discount_amount is always an integer (at least 0)
      const discountAmount = Math.max(
        0,
        Math.floor(originalPrice - discountedPrice)
      );

      // For non-Stripe payments, create payment via API with provided transaction details
      if (data.paymentType !== "Stripe") {
        // Validate required data for non-Stripe payments
        if (!userId || !immigrationServiceId) {
          throw new Error("Missing required data for payment creation");
        }

        // Create payment via API for non-Stripe methods
        const paymentData: CreatePaymentRequest = {
          amount: discountedPrice, // Amount after discount is applied
          user_id: userId,
          serviceType: "immigration",
          serviceId: immigrationServiceId,
          discount_amount: discountAmount, // Always send as integer (at least 0)
          actual_amount: originalPrice, // Original price before discount
          payment_method: paymentMethod as
            | "cash"
            | "bank_deposit"
            | "online_transfer",
          transactionId: data.transactionId, // Optional for all payment methods
        };

        const result = await createPaymentMutation.mutateAsync(paymentData);

        // Proceed to next step with the created payment ID
        onNext({
          paymentMethod: data.paymentType,
          paymentType: data.paymentType,
          paymentId: result.payment_id,
          transactionId: data.transactionId,
          payments: [result.payment_id],
        });
        return;
      }

      // Validate required data for Stripe payments
      if (!userId || !immigrationServiceId) {
        throw new Error("Missing required data for payment creation");
      }

      // For Stripe payments, create payment via API
      // Payload structure matches the specified format:
      // - amount: Product amount after discount is applied (discountedPrice)
      // - discount_amount: The discount amount deducted from actual price (always integer)
      // - actual_amount: Original product price before discount (originalPrice)
      const paymentData: CreatePaymentRequest = {
        amount: discountedPrice, // Amount after discount is applied
        user_id: userId,
        serviceType: "immigration",
        serviceId: immigrationServiceId,
        discount_amount: discountAmount, // Always send as integer (at least 0)
        actual_amount: originalPrice, // Original price before discount
        payment_method: paymentMethod as "stripe",
      };

      const result = await createPaymentMutation.mutateAsync(paymentData);

      if (result.stripe_link) {
        // For Stripe payments: Auto-complete application and redirect to records page
        // This provides a seamless user experience without manual workflow steps
        onNext({
          paymentMethod: data.paymentType,
          paymentType: data.paymentType,
          paymentId: result.payment_id,
          transactionId: undefined, // Not needed for Stripe
          payments: [result.payment_id],
          stripeLink: result.stripe_link, // Pass stripe_link for potential future use
          autoComplete: true, // Flag to indicate auto-completion
        });
        return;

        // End the application creation process here - no automatic redirect
        // User will manually click the Stripe link to complete payment
      } else {
        // Handle case where Stripe link is not returned (shouldn't happen for Stripe)
        throw new Error("Stripe checkout link not received from payment API");
      }
    } catch (error) {
      console.error("Payment creation failed:", error);

      // Enhanced error handling with specific error messages
      let errorMessage = "Failed to process payment. Please try again.";

      if (error instanceof Error) {
        // Handle specific API error messages
        if (error.message.includes("Validation failed")) {
          errorMessage = "Please check your payment details and try again.";
        } else if (error.message.includes("Unauthorized")) {
          errorMessage =
            "Session expired. Please refresh the page and try again.";
        } else if (error.message.includes("Missing required data")) {
          errorMessage =
            "Some required information is missing. Please refresh the page.";
        } else {
          errorMessage = error.message;
        }
      }

      // Set form error for better UX with enhanced error display
      form.setError("root", {
        type: "manual",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Payment Selection
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Display form-level errors */}
            {form.formState.errors.root && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">
                  {form.formState.errors.root.message}
                </p>
              </div>
            )}
            <FormField
              control={form.control}
              name="paymentType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Payment Type <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Stripe">Stripe</SelectItem>
                      <SelectItem value="Cash">Cash</SelectItem>
                      <SelectItem value="Bank Deposit">Bank Deposit</SelectItem>
                      <SelectItem value="Online Transfer">
                        Online Transfer
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Summary - Always show for all payment methods */}
            <div className="p-4 bg-primary/5 rounded-lg border">
              <h4 className="font-medium mb-2">Payment Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Original Price:</span>
                  <span>${originalPrice}</span>
                </div>
                {originalPrice !== discountedPrice && (
                  <div className="flex justify-between">
                    <span>Discount Applied:</span>
                    <span>-${originalPrice - discountedPrice}</span>
                  </div>
                )}
                <div className="flex justify-between font-medium border-t pt-1">
                  <span>Total Amount:</span>
                  <span>${discountedPrice}</span>
                </div>
              </div>
            </div>

            {/* Stripe Payment Link Display - shown when Stripe payment is created */}
            {/* Transaction ID field - now optional for all payment methods */}
            {paymentType && (
              <FormField
                control={form.control}
                name="transactionId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Transaction ID{" "}
                      <span className="text-gray-500">(Optional)</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter transaction ID (optional)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1"
                disabled={loading}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                type="submit"
                disabled={loading || !paymentType}
                className="flex-1"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {paymentType === "Stripe"
                  ? loading
                    ? "Processing Payment & Creating Application..."
                    : "Create Payment & Complete Application"
                  : loading
                    ? "Creating Payment..."
                    : "Create Payment & Continue"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
