"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Workflow,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  Clock,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Validation schema for workflow selection
const workflowSelectionSchema = z.object({
  workflowTemplateId: z.string().min(1, "Please select a workflow template"),
});

type FormData = z.infer<typeof workflowSelectionSchema>;

interface WorkflowSelectionFormProps {
  onNext: (data: { workflowTemplateId: string }) => void;
  onBack: () => void;
  immigrationProductId: string;
  initialData?: { workflowTemplateId?: string };
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  serviceType?: string;
  serviceId?: string;
}

interface WorkflowTemplatesResponse {
  data: WorkflowTemplate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const WorkflowSelectionForm: React.FC<WorkflowSelectionFormProps> = ({
  onNext,
  onBack,
  immigrationProductId,
  initialData,
}) => {
  const { data: session } = useSession();
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(workflowSelectionSchema),
    defaultValues: {
      workflowTemplateId: initialData?.workflowTemplateId || "",
    },
  });

  // Fetch workflow templates filtered by immigration product ID
  useEffect(() => {
    const fetchWorkflowTemplates = async () => {
      if (!session?.backendTokens?.accessToken || !immigrationProductId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch workflow templates filtered by serviceId (immigration product ID)
        const queryParams = new URLSearchParams({
          serviceId: immigrationProductId,
          isActive: "true",
          page: "1",
          limit: "50", // Get up to 50 templates for the service
        });

        const response = await fetch(`/api/workflow-templates?${queryParams}`, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error("Authentication failed. Please sign in again.");
          }
          throw new Error(
            `Failed to fetch workflow templates: ${response.status}`
          );
        }

        const data: WorkflowTemplatesResponse = await response.json();

        if (data.data && Array.isArray(data.data)) {
          setTemplates(data.data);

          // If no templates found for this service, show appropriate message
          if (data.data.length === 0) {
            setError(
              "No workflow templates are available for the selected service. Please contact support."
            );
          }
        } else {
          setError("Invalid response format from server.");
        }
      } catch (err) {
        console.error("Error fetching workflow templates:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Failed to load workflow templates. Please try again."
        );
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflowTemplates();
  }, [session, immigrationProductId]);

  const onSubmit = async (data: FormData) => {
    setSubmitting(true);
    try {
      // Validate that the selected template exists in our list
      const selectedTemplate = templates.find(
        (t) => t.id === data.workflowTemplateId
      );
      if (!selectedTemplate) {
        throw new Error("Selected workflow template is no longer available.");
      }

      onNext({
        workflowTemplateId: data.workflowTemplateId,
      });
    } catch (err) {
      console.error("Error submitting workflow selection:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Failed to proceed. Please try again."
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Workflow className="h-5 w-5" />
          Select Workflow Template
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Choose a workflow template that defines the process steps for this
          immigration application. Templates are customized for your selected
          service and will guide the application through completion.
        </p>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="relative">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Workflow className="h-8 w-8 text-blue-600" />
              </div>
              <Loader2 className="h-6 w-6 animate-spin absolute -top-1 -right-1 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Loading Workflow Templates
            </h3>
            <p className="text-sm text-gray-600">
              Fetching available templates for your selected service...
            </p>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : templates.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Workflow className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Workflow Templates Available
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              No workflow templates are configured for the selected immigration
              service.
            </p>
            <Alert className="text-left">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please contact your administrator to set up workflow templates
                for this service, or go back and select a different service.
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="workflowTemplateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Available Workflow Templates</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="space-y-3"
                      >
                        {templates.map((template) => (
                          <div
                            key={template.id}
                            className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 hover:border-blue-200 transition-all duration-200 cursor-pointer"
                          >
                            <RadioGroupItem
                              value={template.id}
                              id={template.id}
                              className="mt-1"
                            />
                            <div className="flex-1 space-y-2">
                              <div className="flex items-center justify-between">
                                <label
                                  htmlFor={template.id}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  {template.name}
                                </label>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant={
                                      template.isActive
                                        ? "default"
                                        : "secondary"
                                    }
                                    className="text-xs"
                                  >
                                    <CheckCircle2 className="w-3 h-3 mr-1" />
                                    {template.isActive ? "Active" : "Inactive"}
                                  </Badge>
                                </div>
                              </div>
                              {template.description && (
                                <p className="text-sm text-gray-600 leading-relaxed">
                                  {template.description}
                                </p>
                              )}
                              <div className="flex items-center text-xs text-gray-500 mt-2">
                                <Clock className="w-3 h-3 mr-1" />
                                <span>Service-specific workflow template</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onBack}
                  disabled={submitting}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <Button
                  type="submit"
                  disabled={submitting || templates.length === 0}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
};
